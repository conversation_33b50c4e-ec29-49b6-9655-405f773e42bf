import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/models/nurture_types.dart';
import 'package:flower_timemachine/pages/add_flower/controller/add_flower_controller.dart';
import 'package:flutter/material.dart';

import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_picker_plus/flutter_picker_plus.dart';

class AddFlowerAlarmWidget extends ConsumerWidget {
  const AddFlowerAlarmWidget({
    super.key,
    required this.type,
    required this.pickerData,
    required this.flower,
  });

  final NurtureType type;
  final List<String> pickerData;
  final Flower? flower;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 使用 select 监听特定的 nurture cycle
    final cycle = ref.watch(
      addFlowerControllerProvider(flower).select((asyncState) =>
        asyncState.when(
          data: (state) => state.getNurtureTypeCycle(type),
          loading: () => 0,
          error: (_, __) => 0,
        )
      )
    );

    Widget cycleText;
    if (cycle <= 0) {
      cycleText = Text("unset".tr(), style: const TextStyle(color: Colors.black54));
    } else {
      cycleText = const Text("add_flower.cycle").tr(
        namedArgs: {"day": pickerData[cycle]}
      );
    }

    var picker = Picker(
        title: Text(type.name),
        adapter: PickerDataAdapter<String>(pickerData: pickerData),
        cancelText: 'cancel'.tr(),
        confirmText: 'confirm'.tr(),
        onConfirm: (picker, selected) => onConfirm(ref, selected)
    );

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      child: buildBody(cycleText),
      onTap: () => picker.showModal(context),
    );
  }

  Widget buildBody(Widget cycle) {
    return Container(
        padding: const EdgeInsets.all(5),
        child: Row(children: [
          Container(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: SvgPicture.asset(type.icon, width: 16, height: 16),
          ),
          Text(type.name),
          const Spacer(),
          cycle,
          const SizedBox(width: 5),
          const Icon(Icons.edit)
        ])
    );
  }

  void onConfirm(WidgetRef ref, List<int> selected) {
    ref.read(addFlowerControllerProvider(flower).notifier).setNurtureTypeCycle(type, selected[0]);
  }
}