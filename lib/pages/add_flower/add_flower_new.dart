import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/common/dialog_utils.dart';
import 'package:flower_timemachine/pages/add_flower/controller/add_flower_controller.dart';
import 'package:flower_timemachine/pages/monthly_cycle_settings/monthly_cycle_settings_page.dart';
import 'package:flower_timemachine/types/flower_monthly_cycles.dart';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/flower.dart';
import '../../models/tag_info.dart';
import '../../widgets/ftm_box.dart';
import 'widgets/add_flower_tag_selector.dart';
import 'widgets/alarm_list_widget.dart';
import 'widgets/arrival_time_widget.dart';
import 'widgets/avatar_changeable.dart';

final selectItem1 = Iterable.generate(366, (i) => '$i').toList();

class AddFlowerPage extends ConsumerStatefulWidget {
  const AddFlowerPage({this.flower, super.key});

  final Flower? flower;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _AddFlowerPageState();
}

class _AddFlowerPageState extends ConsumerState<AddFlowerPage> {
  final GlobalKey _formKey = GlobalKey<FormState>();
  TextEditingController? _nameController;
  final List<TagInfo> selectedTags = [];

  @override
  Widget build(BuildContext context) {
    final controllerAsync = ref.watch(addFlowerControllerProvider(widget.flower));

    return Scaffold(
        appBar: AppBar(
          centerTitle: true,
          title: Text(widget.flower == null ? "record_new_flower".tr() : "editing".tr()),
          elevation: 0,
          actions: [TextButton(onPressed: () => onSave(controllerAsync), child: const Text("save").tr())],
          leading: TextButton(onPressed: () => onCancel(controllerAsync), child: const Text("cancel").tr()),
          leadingWidth: 68,
        ),
        body: PopScope(
            canPop: false,
            child: controllerAsync.when(
              data: (state) => SingleChildScrollView(
                child: Container(
                  padding: const EdgeInsets.only(left: 10, right: 10, top: 10),
                  child: Column(
                    children: [
                      buildBaseInfoWidget(state),
                      buildAlarmInfoWidget(state),
                      buildOtherOption(state)
                    ],
                  ),
                )
              ),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Center(child: Text('Error: $error')),
            )
        )
    );
  }

  Widget buildBaseInfoWidget(AddFlowerState state) {
    if (_nameController == null) {
      _nameController = TextEditingController();
      _nameController!.text = state.name ?? '';
    }

    return FTMBox(
      circular: 10,
      child: Form(
        key: _formKey,
        child: Container(
            padding: const EdgeInsets.all(10),
            child: Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  AvatarChangeableWidget(
                      height: 65,
                      width: 65,
                      avatar: state.avatar,
                      onChange: (imageData) {
                        ref.read(addFlowerControllerProvider(widget.flower).notifier).setNewAvatar(imageData);
                  }),
                  const SizedBox(width: 10),
                  Flexible(child: TextFormField(
                      controller: _nameController,
                      validator: nameValidator,
                      decoration: InputDecoration(hintText: "flower_name_edit_prompt".tr())
                  )),
                ]
            )
        ),
      ),
    );
  }

  Widget buildAlarmInfoWidget(AddFlowerState state) {
    return FTMBox(
        circular: 10,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 5),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => onAdvancedSettings(state),
                    child: Text(
                      'advanced'.tr(),
                      style: const TextStyle(
                        color: Colors.black54,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
              AddFlowerAlarmListWidget(flower: widget.flower),
            ],
          ),
        )
    );
  }



  Widget buildOtherOption(AddFlowerState state) {
    return FTMBox(
        circular: 10,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 5),
          child: Column(
            children: [
              AddFlowerTagSelector(selectedTags: selectedTags),
              ArrivalTimeWidget(flower: widget.flower)
            ],
          ),
        )
    );
  }

  String? nameValidator(v) {
    if (v!.trim().isEmpty) {
      return "add_flower.flower_name_cannot_empty".tr();
    }
    return null;
  }


  void onSave(AsyncValue<AddFlowerState> controllerAsync) async {
    if (!(_formKey.currentState as FormState).validate()) {
      return;
    }

    final state = controllerAsync.value;
    if (state == null) return;

    final controller = ref.read(addFlowerControllerProvider(widget.flower).notifier);
    controller.setSelectedTags(selectedTags);
    controller.setName(_nameController?.text);

    if (!state.isChange) {
      Navigator.pop(context, null);
      return;
    }

    controller.save().then((flower) => Navigator.pop(context, flower));
  }

  void onCancel(AsyncValue<AddFlowerState> controllerAsync) async {
    final state = controllerAsync.value;
    if (state == null) return;

    final controller = ref.read(addFlowerControllerProvider(widget.flower).notifier);
    controller.setSelectedTags(selectedTags);
    controller.setName(_nameController?.text);

    if (state.isChange) {
      final isExit = await showAlertDialog('alert'.tr(), 'unsave_alert'.tr(), context);
      if (isExit == null || !isExit) {
        return;
      }
    }

    if (context.mounted) {
      Navigator.pop(context, null);
    }
  }

  void onAdvancedSettings(AddFlowerState state) async {
    final result = await Navigator.push<FlowerMonthlyCycles>(
      context,
      MaterialPageRoute(
        builder: (context) => MonthlyCycleSettingsPage(
          initialCycles: state.monthlyCycles,
        ),
      ),
    );

    if (result != null) {
      ref.read(addFlowerControllerProvider(widget.flower).notifier).updateMonthlyCycles(result);
    }
  }

}
