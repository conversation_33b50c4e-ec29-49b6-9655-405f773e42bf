import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/controller/nurture_types_controller.dart';
import 'package:flower_timemachine/models/nurture_types.dart';
import 'package:flower_timemachine/types/flower_monthly_cycles.dart';


import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class MonthlyCycleSettingsPage extends StatefulWidget {
  const MonthlyCycleSettingsPage({
    super.key,
    required this.initialCycles,
  });

  final FlowerMonthlyCycles initialCycles;

  @override
  State<MonthlyCycleSettingsPage> createState() => _MonthlyCycleSettingsPageState();
}

class _MonthlyCycleSettingsPageState extends State<MonthlyCycleSettingsPage>
    with SingleTickerProviderStateMixin {
  late FlowerMonthlyCycles _cycles;
  late TabController _tabController;
  late List<NurtureType> _nurtureTypes;
  int _selectedNurtureTypeIndex = 0;
  final List<String> _monthNames = [
    'monthly_cycle_settings.months.january'.tr(),
    'monthly_cycle_settings.months.february'.tr(),
    'monthly_cycle_settings.months.march'.tr(),
    'monthly_cycle_settings.months.april'.tr(),
    'monthly_cycle_settings.months.may'.tr(),
    'monthly_cycle_settings.months.june'.tr(),
    'monthly_cycle_settings.months.july'.tr(),
    'monthly_cycle_settings.months.august'.tr(),
    'monthly_cycle_settings.months.september'.tr(),
    'monthly_cycle_settings.months.october'.tr(),
    'monthly_cycle_settings.months.november'.tr(),
    'monthly_cycle_settings.months.december'.tr()
  ];

  @override
  void initState() {
    super.initState();
    _cycles = widget.initialCycles.copy();
    _nurtureTypes = NurtureTypesController.get().enableTypes;
    _tabController = TabController(length: _nurtureTypes.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        centerTitle: true,
        title: Text('monthly_cycle_settings.title'.tr()),
        elevation: 0,
        backgroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: _onSave,
            child: const Text("save").tr(),
          )
        ],
        leading: TextButton(
          onPressed: _onCancel,
          child: const Text("cancel").tr(),
        ),
        leadingWidth: 68,
      ),
      body: Column(
        children: [
          _buildNurtureTypeSelector(),
          Expanded(
            child: _buildCurrentNurtureTypeView(),
          ),
        ],
      ),
    );
  }

  Widget _buildNurtureTypeSelector() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 12),
          SizedBox(
            height: 80,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _nurtureTypes.length,
              itemBuilder: (context, index) {
                final type = _nurtureTypes[index];
                final isSelected = index == _selectedNurtureTypeIndex;
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedNurtureTypeIndex = index;
                    });
                  },
                  child: Container(
                    width: 80,
                    margin: const EdgeInsets.only(right: 12),
                    decoration: BoxDecoration(
                      color: isSelected ? Theme.of(context).primaryColor.withOpacity(0.1) : Colors.grey[100],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected ? Theme.of(context).primaryColor : Colors.transparent,
                        width: 2,
                      ),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SvgPicture.asset(
                          type.icon,
                          width: 24,
                          height: 24,
                          colorFilter: ColorFilter.mode(
                            isSelected ? Theme.of(context).primaryColor : Colors.grey[600]!,
                            BlendMode.srcIn,
                          ),
                        ),
                        const SizedBox(height: 6),
                        Text(
                          type.name,
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                            color: isSelected ? Theme.of(context).primaryColor : Colors.grey[600],
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentNurtureTypeView() {
    if (_nurtureTypes.isEmpty) return const SizedBox();
    final currentType = _nurtureTypes[_selectedNurtureTypeIndex];

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTypeHeader(currentType),
          const SizedBox(height: 16),
          Expanded(
            child: _buildMonthlyCalendar(currentType),
          ),
        ],
      ),
    );
  }

  Widget _buildTypeHeader(NurtureType type) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: SvgPicture.asset(
              type.icon,
              width: 20,
              height: 20,
              colorFilter: ColorFilter.mode(
                Theme.of(context).primaryColor,
                BlendMode.srcIn,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  type.name,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  type.defaultCycle == -1
                    ? '${'monthly_cycle_settings.default_cycle'.tr()}: ${'monthly_cycle_settings.default_cycle_unset'.tr()}'
                    : '${'monthly_cycle_settings.default_cycle'.tr()}: ${'monthly_cycle_settings.default_cycle_days'.tr(namedArgs: {'days': type.defaultCycle.toString()})}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildMonthlyCalendar(NurtureType type) {
    final currentMonth = DateTime.now().month;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'monthly_cycle_settings.title'.tr(),
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                'monthly_cycle_settings.current_month'.tr(namedArgs: {'month': _monthNames[currentMonth - 1]}),
                style: TextStyle(
                  fontSize: 11,
                  color: Theme.of(context).primaryColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Expanded(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: ListView.builder(
              itemCount: 12,
              itemBuilder: (context, index) => _buildMonthCard(type, index + 1),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMonthCard(NurtureType type, int month) {
    final cycle = _cycles.getCycleForTypeAndMonth(type, month);
    final effectiveCycle = _cycles.getEffectiveCycleForTypeAndMonth(type, month);
    final currentMonth = DateTime.now().month;
    final isCurrentMonth = month == currentMonth;

    String displayText;
    String? subtitleText;
    Color textColor;

    if (cycle == -1) {
      displayText = 'monthly_cycle_settings.unset'.tr();
      textColor = Colors.grey[600]!;
    } else if (cycle == 0) {
      if (effectiveCycle != -1) {
        displayText = '$effectiveCycle${'monthly_cycle_settings.days_unit'.tr()}';
        subtitleText = 'monthly_cycle_settings.inherit_setting'.tr();
      } else {
        displayText = 'monthly_cycle_settings.unset'.tr();
        subtitleText = 'monthly_cycle_settings.inherit_setting'.tr();
      }
      textColor = Colors.grey[700]!;
    } else {
      displayText = '$cycle${'monthly_cycle_settings.days_unit'.tr()}';
      textColor = Colors.black87;
    }

    return InkWell(
      onTap: () => _showCycleSelector(type, month),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 12),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Colors.grey[200]!,
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          children: [
            SizedBox(
              width: 50,
              child: Row(
                children: [
                  Text(
                    _monthNames[month - 1],
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: isCurrentMonth ? FontWeight.bold : FontWeight.normal,
                      color: isCurrentMonth ? Theme.of(context).primaryColor : Colors.grey[800],
                    ),
                  ),
                  if (isCurrentMonth)
                    Container(
                      margin: const EdgeInsets.only(left: 4),
                      width: 4,
                      height: 4,
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor,
                        shape: BoxShape.circle,
                      ),
                    ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    displayText,
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w500,
                      color: textColor,
                    ),
                  ),
                  if (subtitleText != null)
                    Text(
                      subtitleText,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[500],
                      ),
                    ),
                ],
              ),
            ),
            Icon(
              Icons.chevron_right,
              size: 18,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }

  void _showCycleSelector(NurtureType type, int month) {
    final currentCycle = _cycles.getCycleForTypeAndMonth(type, month);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => _buildCycleSelectorSheet(type, month, currentCycle),
    );
  }

  Widget _buildCycleSelectorSheet(NurtureType type, int month, int currentCycle) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "${type.name} - ${_monthNames[month - 1]}",
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),

          // 继承选项
          _buildCycleOption(
            title: 'monthly_cycle_settings.inherit'.tr(),
            subtitle: 'monthly_cycle_settings.inherit_description'.tr(),
            value: 0,
            currentValue: currentCycle,
            onTap: () => _setCycle(type, month, 0),
          ),

          // 未设置选项
          _buildCycleOption(
            title: 'monthly_cycle_settings.unset'.tr(),
            subtitle: 'monthly_cycle_settings.unset_description'.tr(),
            value: -1,
            currentValue: currentCycle,
            onTap: () => _setCycle(type, month, -1),
          ),

          // 自定义天数选项
          _buildCustomCycleOption(type, month, currentCycle),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildCycleOption({
    required String title,
    required String subtitle,
    required int value,
    required int currentValue,
    required VoidCallback onTap,
  }) {
    final isSelected = currentValue == value;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected ? Theme.of(context).primaryColor.withOpacity(0.1) : Colors.grey[50],
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: isSelected ? Theme.of(context).primaryColor : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.bold,
                      color: isSelected ? Theme.of(context).primaryColor : Colors.black87,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: Theme.of(context).primaryColor,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomCycleOption(NurtureType type, int month, int currentCycle) {
    final isCustom = currentCycle > 0;
    final controller = TextEditingController(
      text: isCustom ? currentCycle.toString() : '',
    );

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isCustom ? Theme.of(context).primaryColor.withOpacity(0.1) : Colors.grey[50],
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: isCustom ? Theme.of(context).primaryColor : Colors.grey[300]!,
          width: isCustom ? 2 : 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'monthly_cycle_settings.custom_days'.tr(),
            style: TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.bold,
              color: isCustom ? Theme.of(context).primaryColor : Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: controller,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    hintText: 'monthly_cycle_settings.input_days_hint'.tr(),
                    border: const OutlineInputBorder(),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                    hintStyle: TextStyle(color: Colors.grey[500]),
                  ),
                  onSubmitted: (value) {
                    final days = int.tryParse(value);
                    if (days != null && days >= 1 && days <= 366) {
                      _setCycle(type, month, days);
                    }
                  },
                ),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
                onPressed: () {
                  final days = int.tryParse(controller.text);
                  if (days != null && days >= 1 && days <= 366) {
                    _setCycle(type, month, days);
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('monthly_cycle_settings.invalid_days_error'.tr())),
                    );
                  }
                },
                child: Text('confirm'.tr()),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _setCycle(NurtureType type, int month, int cycle) {
    setState(() {
      _cycles.setCycleForTypeAndMonth(type, month, cycle);
    });
    Navigator.pop(context);
  }

  void _onSave() {
    Navigator.pop(context, _cycles);
  }

  void _onCancel() {
    Navigator.pop(context);
  }
}
