{"app_name": "Plant Time", "app_slogan": "Capture every moment worth remembering in your plant care journey", "garden": "Garden", "setting": "Setting", "hour": "Hour", "minute": "Minute", "alert": "<PERSON><PERSON>", "today": "today", "yesterday": "yesterday", "tomorrow": "tomorrow", "future_day": "in {day} days", "unset": "UNSET", "confirm": "Confirm", "cancel": "Cancel", "save": "Save", "from_camera": "Captured by a camera", "from_album": "Select from the album", "record_new_flower": "Recording a new flower", "editing": "Editing", "delete": "Delete", "flower_name_edit_prompt": "Please enter the flower's name", "free_user_limit": "Professional edition features", "edit_avatar": "Edit avatar", "prompt": "Prompt", "system_error_and_retry": "Seems like there's a little glitch in the system. Please try again.", "system_error_and_contact": "System error. Please contact the developer", "date": "Date", "time": "Time", "publish": "Publish", "enable": "Enable", "disable": "Disable", "tag_manger": "Tag management", "waiting": "Just wait a moment", "cannot_be_empty": "Cannot be empty", "characters_length_limit": "Cannot exceed {length} characters", "care_reminder": "Care reminder", "care_reminder_content": "Plants needing care today", "all": "All", "unsave_alert": "There are unsaved changes. Are you sure you want to exit?", "system_error_and_restart": "System error. Please restart the app", "system_error_code": "System error, Code: {code}", "language": "Language", "select_language": "Select Language", "check_all": "Check all", "inverse": "Inverse", "nurture_reminder": {"no_task": "Reminder interval not set", "overdue": "{name} overdue by {day} days", "future": "{nurture} needed {day}", "today": "{nurture} needed today", "companion": "", "companion_days": "/days", "years_hint": "{year} years ago", "month_hint": "{month} months ago", "day_hint": "{day} days ago", "last_maintenance": "Last {type} at {time}"}, "add_flower": {"tag_title": "  Tag", "add_tag": "Add Tag", "cycle": "Every {day} days", "flower_name_cannot_empty": "The name cannot be empty.", "flower_name_length_limit": "The name cannot exceed {length} characters", "arrival_time_title": "  Arrival Time"}, "add_nurture": {"title_new": "New", "title_edit": "Editing", "default_care_cycle": "Default care cycle", "nurture_name_prompt": "Please enter care name", "nurture_name_empty": "Care name cannot be empty", "nurture_name_length_limit": "Care name cannot exceed 16 characters"}, "backup": {"auto_backup": "Auto backup", "upload_now": "Upload now", "backup_state": "Backup status", "no_backup": "Not backed up", "icloud_no_data": "No data in iCloud", "no_signed": "Not signed in to Apple ID or iCloud disabled", "latest_upload": "Latest upload", "latest_download": "Latest download", "last_restore": "Last restore", "backing": "Backing up", "downloading": "Downloading", "download": "Download backup", "restore_backup": "Restore backup", "backup_data_not_reader": "Backup data not ready. Please download again.", "restore_alert": "Restoring data will overwrite all current data. Are you sure you want to restore?", "restoring": "Restoring", "restore_success": "Restoration successful", "title": "Backup data", "instructions": "Instructions:\n1. Backed-up data will be uploaded to your Apple account.\n2. Using the same account allows data backup/restore across different iphone.\n3. Restoring a backup will overwrite current data.\n4. Automatic backup occurs on the first launch each day.\n5. Upload and download functions can be used even with the app closed.\n6. Restoring a backup requires clicking on 'Download Backup' first.\n7. If you have purchased the professional edition and need to restore data, please use the restore purchase function on the purchase page.", "icloud_unavailable": "iCloud permissions are disabled.\nPlease check Settings -> iCloud -> Allow Apps to use iCloud and open iCloud Cloud Drive.", "auto_back_failed_and_icloud_disabled": "Automatic backup failed as iCloud permissions were disabled.\nPlease check Settings -> iCloud -> Allow App to Use iCloud and ensure iCloud Drive is enabled."}, "timeline": {"first_photo": "Can you help me record the details of the first photo?", "delete_alert": "Are you sure you want to delete the record from {date}, at {time}?"}, "nurture": {"first_care": "Feel free to add care records instantly through the top-right button", "delete_alert": "Are you sure you want to delete the {type} record from {date}?"}, "flower_detail": {"edit_mode": "Edit mode", "share": "Share", "delete": "Delete", "record_after_today": "Records can only be added for past and today, not allowed for the future", "duplicate_record": "There's already a '{type}' record for {date}", "no_need_nurture_record": "No care records needed for today.", "auto_nurture_part": "Only completed {types} care; the rest might have been done. Please restart the app to check.", "delete_alert": "Are you sure you want to delete the {name}? Once deleted, the data cannot be recovered.", "tab_care": "Care record", "tab_timeline": "Time machine", "tab_note": "Note"}, "flower_share": {"next": "Next", "share": "Share", "screenshot_failed": "Screenshot generation failed. Please try again later", "no_photos": "No photos available"}, "flower_timeline_editor": {"text_hint": "Just wondering, what's on your mind?", "no_future_time": "Unable to set a future time", "publish_limit": "Please enter text or select at least one photo", "image_max_limit": "Select up to {number} of photos", "add_image_permission": "We need album permissions to add new images", "camera_permission": "We need camera access to take pictures"}, "garden_tag": {"no_flower": "No plants under this tag", "first_flower": "Click the + button in the top right corner to add your first flower", "flower_number": "There are {number} flowers in total", "garden_empty": "Your garden is empty"}, "home_page": {"batch_care": "Batch care", "auto_care": "Auto Care", "sort_care_asc": "Sort by care time, ascending", "sort_care_desc": "Sort by care time, descending", "sort_companion_asc": "Sort by companion time, ascending", "sort_companion_desc": "Sort by companion time, descending", "sort_name_asc": "Sort by name, ascending", "sort_name_desc": "Sort by name, descending", "list_view": "List View", "grid_view": "Grid View"}, "nurture_manager": {"care_task": "Care task management", "least_one_care": "At least one care task must be enabled", "delete_title": "Delete {name}", "delete_alert": "Once deleted, all {name}-related care records will be lost. Are you sure you want to delete?"}, "photo_picker": {"max_photo": "Select up to {max} images at most.", "photo_permission": "We need access to your photo library to save newly captured photos.", "camera_permission": "We require camera access to take photos", "show_photo_permission": "We need access to your photo library to display photos"}, "photo_date_picker": {"title": "Photo Date Selection", "subtitle": "Choose a date for your timeline record", "photo_count": "{count} photos"}, "remind_manager": {"title": "Reminder management", "no_remind": "No reminders at the moment.", "cancel": "Cancel reminder", "cancel_success": "Cancellation successful"}, "search_page": {"no_result": "No results found", "input_hint": "Please enter the name of the plant", "flower_number_hint": "You now have {number} plants !"}, "setting_page": {"vip": "Professional edition", "remind_time": "Reminder time", "remind_manager": "Reminder management", "nurture_manger": "Care task management", "show_companion_days": "Plant companionship duration", "show_album_timeline": "Show album timeline", "score": "Rate", "app_log": "App log", "contact_me": "Contact me", "backup": "Backup", "version": "Version", "score_android": "Not yet supporting ratings on the Android platform", "check_update": "Checking for new version", "already_latest_version": "You're already on the latest version", "copy_email": "Email copied to clipboard", "show_only_set_nurture_types": "Show only set nurture types", "show_future_nurture": "Show future nurture", "show_history_nurture": "Show history nurture", "auto_detect_photo_date": "Auto detect photo date", "auto_detect_photo_date_vip_only": "Auto detect photo date is a VIP feature"}, "show_photo_page": {"save_album": "Saved to photo album", "save_success": "Save successful"}, "tag_manager_page": {"first_tag": "Click the + in the top right corner to add the first tag", "free_user_limit": "Up to 5 tags allowed for the non-professional version", "enter_tag_name": "Enter the new tag name", "same_tag": "There is already a tag with the same name", "delete_title": "Delete {name}", "delete_content": "Are you sure you want to delete this tag?\nNote: Deleting the tag will not remove the associated plants", "select_tag": "Select tags", "select_tag_limit": "You can select up to 3 tags at most", "add_tag": "+ Create a new tag"}, "vip_page": {"title": "Professional edition", "refuse_message": "Apple notified us of your refund; the professional version has been deactivated.", "invalid_message": "The previous transaction has been verified as invalid; the membership has been deactivated.", "query_price_failed": "Failed to retrieve price", "loading_price": "Loading prices", "purchase_failed": "Purchase failed", "restore_purchase": "Rest<PERSON>", "already_vip": "you've got the Pro version already", "restore_failed_title": "Rest<PERSON> failed", "restore_failed_content": "No purchase records found, unable to restore", "purchase_success": "Purchase successful", "purchase_verify_failed": "Failed to verify purchase data: {user}", "cancel_purchase": "Cancel purchase", "next_purchase": "Purchase next time", "now_purchase": "Go buy it now", "items": {"no_limit_tag": "Unlimited tags", "quick_search": "Quick search", "cloud_backup": "Cloud backup", "custom_nurture": "Custom care tasks", "auto_nurture_type": "One-click add specified care tasks", "forever": "Lifetime purchase"}}, "avatar_changeable_page": {"add_avatar_permission": "We need access to your photo library to add a new avatar", "camera_permission": "We need camera access to take photos"}, "permission_dialog": {"no_permission": "No permissions", "go_setting": "Go to settings"}, "update_dialog": {"tips": "New Version", "next_tips": "Remind me Later", "skip": "Skip This Version", "upgrade": "Upgrade Now"}, "care_type": {"water": "Watering", "fertilizer": "Fertilizing", "cut": "Pruning", "pest_control": "Pest control"}, "archive_info": {"title": "Archive Information", "subtitle": "Fill archive information for {name}", "date": "Archive Date", "time": "Archive Time", "reason": "Archive Reason", "reason_hint": "Enter archive reason (optional)"}, "archive_page": {"archive_time": "Archive time：{date}", "archive_reason": "Archive reason: {reason}", "empty_data": "Flowers not archived"}, "archive": "Archive", "remark_hint": "Please enter remarks", "album": "Album", "album_empty": "No photos yet, please add them in the timeline", "loading": "Loading", "no_nurture_type": "Please add care items first", "garden_title": "Garden Name", "calendar": "Calendar", "calendar_no_events": "No records", "long_press_to_reorder": "Long press to reorder", "saveing": "Saving...", "export": "Export", "error": "Error", "system_error": "System Error", "search": "Search", "monthly_cycle_settings": {"title": "Monthly Cycle Settings", "months": {"january": "Jan", "february": "Feb", "march": "Mar", "april": "Apr", "may": "May", "june": "Jun", "july": "Jul", "august": "Aug", "september": "Sep", "october": "Oct", "november": "Nov", "december": "Dec"}, "default_cycle": "Default cycle", "default_cycle_unset": "Unset", "default_cycle_days": "{days} days", "current_month": "Current {month}", "unset": "Unset", "inherit_setting": "Inherited", "days_unit": "days", "inherit": "Inherit", "inherit_description": "Use previous month's setting", "unset_description": "No care for this month", "custom_days": "Custom days", "input_days_hint": "Enter days (1-366)", "invalid_days_error": "Please enter a number between 1-366"}, "advanced": "Advanced"}